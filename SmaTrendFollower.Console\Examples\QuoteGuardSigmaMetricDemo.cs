using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Demonstrates the QuoteGuard σ Prometheus Metric functionality
/// Shows how the metric provides early warning before trading halts
/// </summary>
public static class QuoteGuardSigmaMetricDemo
{
    public static async Task RunAsync()
    {
        Console.WriteLine("=== QuoteGuard σ Prometheus Metric Demo ===\n");

        // Setup
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<QuoteVolatilityGuard>();

        var options = new QuoteVolatilityOptions
        {
            WindowSeconds = 120,
            StdDevSigma = 2.0,  // 2σ threshold
            HaltDurationSeconds = 120
        };

        var guard = new QuoteVolatilityGuard(options, logger);

        Console.WriteLine($"QuoteVolatilityGuard Configuration:");
        Console.WriteLine($"- Window: {options.WindowSeconds} seconds");
        Console.WriteLine($"- Threshold: {options.StdDevSigma}σ");
        Console.WriteLine($"- Halt Duration: {options.HaltDurationSeconds} seconds\n");

        // Demo 1: Normal volatility
        Console.WriteLine("📊 Demo 1: Normal Market Conditions");
        Console.WriteLine("Sending quotes with normal volatility...");
        
        for (int i = 0; i < 25; i++)
        {
            var basePrice = 100.00m;
            var smallVariation = (decimal)(0.1 * Math.Sin(i * 0.1)); // ±0.1% variation
            var price = basePrice + smallVariation;
            
            guard.OnQuote("AAPL", price - 0.05m, price + 0.05m);
            
            if (i % 5 == 0)
            {
                var ratio = MetricsRegistry.QuoteGuardSigmaRatio.Value;
                Console.WriteLine($"  Quote {i + 1:D2}: Price={price:F2}, σ/threshold ratio={ratio:F3}");
            }
        }

        var finalRatio1 = MetricsRegistry.QuoteGuardSigmaRatio.Value;
        Console.WriteLine($"✅ Normal conditions: Final σ/threshold ratio = {finalRatio1:F3} (< 1.0 = safe)\n");

        // Demo 2: Building pressure
        Console.WriteLine("⚠️  Demo 2: Volatility Building Up");
        Console.WriteLine("Sending quotes with increasing volatility...");

        for (int i = 0; i < 25; i++)
        {
            var basePrice = 100.00m;
            var increasingVariation = (decimal)(0.5 + (i * 0.1)) * (decimal)Math.Sin(i * 0.3); // Increasing volatility
            var price = basePrice + increasingVariation;
            
            guard.OnQuote("MSFT", price - 0.05m, price + 0.05m);
            
            if (i % 5 == 0)
            {
                var ratio = MetricsRegistry.QuoteGuardSigmaRatio.Value;
                var status = ratio > 1.5 ? "🚨 ALERT" : ratio > 1.0 ? "⚠️  WARNING" : "✅ OK";
                Console.WriteLine($"  Quote {i + 1:D2}: Price={price:F2}, σ/threshold ratio={ratio:F3} {status}");
            }
        }

        var finalRatio2 = MetricsRegistry.QuoteGuardSigmaRatio.Value;
        Console.WriteLine($"⚠️  Building pressure: Final σ/threshold ratio = {finalRatio2:F3}\n");

        // Demo 3: Extreme volatility (should trigger halt)
        Console.WriteLine("🚨 Demo 3: Extreme Volatility (Halt Trigger)");
        Console.WriteLine("Sending quotes with extreme volatility...");

        var extremePrices = new decimal[] { 95m, 105m, 90m, 110m, 85m, 115m, 80m, 120m, 75m, 125m,
                                          95m, 105m, 90m, 110m, 85m, 115m, 80m, 120m, 75m, 125m,
                                          95m, 105m, 90m, 110m, 85m };

        for (int i = 0; i < extremePrices.Length; i++)
        {
            var price = extremePrices[i];
            guard.OnQuote("TSLA", price - 0.05m, price + 0.05m);
            
            if (i % 5 == 0)
            {
                var ratio = MetricsRegistry.QuoteGuardSigmaRatio.Value;
                var status = ratio > 1.5 ? "🚨 HALT RISK" : ratio > 1.0 ? "⚠️  WARNING" : "✅ OK";
                Console.WriteLine($"  Quote {i + 1:D2}: Price={price:F2}, σ/threshold ratio={ratio:F3} {status}");
            }
        }

        var finalRatio3 = MetricsRegistry.QuoteGuardSigmaRatio.Value;
        Console.WriteLine($"🚨 Extreme volatility: Final σ/threshold ratio = {finalRatio3:F3} (> 1.0 = halt triggered)\n");

        // Summary
        Console.WriteLine("📈 Grafana Alert Recommendations:");
        Console.WriteLine("- Set alert at σ/threshold ratio > 1.5 for early warning");
        Console.WriteLine("- Set critical alert at σ/threshold ratio > 2.0 for immediate action");
        Console.WriteLine("- Monitor trend over time to detect building pressure");
        Console.WriteLine("\n🎯 Benefits:");
        Console.WriteLine("- Early warning before trading halts");
        Console.WriteLine("- Time to widen spreads or pause bot gracefully");
        Console.WriteLine("- Visibility into quote volatility pressure");
        Console.WriteLine("- Historical data for post-incident analysis");

        Console.WriteLine("\n=== Demo Complete ===");
    }
}
